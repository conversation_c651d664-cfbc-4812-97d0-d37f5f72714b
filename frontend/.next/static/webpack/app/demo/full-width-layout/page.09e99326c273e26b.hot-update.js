"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/demo/full-width-layout/page",{

/***/ "(app-pages-browser)/./src/components/layout/full-width/PageTransition.tsx":
/*!*************************************************************!*\
  !*** ./src/components/layout/full-width/PageTransition.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContentTransition: function() { return /* binding */ ContentTransition; },\n/* harmony export */   PageTransition: function() { return /* binding */ PageTransition; },\n/* harmony export */   WidthTransition: function() { return /* binding */ WidthTransition; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ PageTransition,ContentTransition,WidthTransition auto */ \n/**\n * 页面过渡动画组件\n * 专为全宽布局优化，确保从列表页到详情页的平滑过渡\n * 临时简化版本，使用CSS动画替代framer-motion\n */ function PageTransition({ children, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `animate-fade-in-up ${className || \"\"}`,\n        style: {\n            animationDuration: \"0.6s\",\n            animationTimingFunction: \"cubic-bezier(0.25, 0.46, 0.45, 0.94)\"\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/full-width/PageTransition.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_c = PageTransition;\n/**\n * 内容区域过渡动画\n * 用于详情页内容的渐进式显示\n */ function ContentTransition({ children, delay = 0, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n        initial: {\n            opacity: 0,\n            y: 20\n        },\n        animate: {\n            opacity: 1,\n            y: 0,\n            transition: {\n                duration: 0.5,\n                delay,\n                ease: [\n                    0.25,\n                    0.46,\n                    0.45,\n                    0.94\n                ]\n            }\n        },\n        className: className,\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/full-width/PageTransition.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ContentTransition;\n/**\n * 宽度过渡动画\n * 专门用于容器宽度的平滑变化\n */ function WidthTransition({ children, fromWidth = \"max-w-5xl\", toWidth = \"max-w-7xl\", className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n        initial: {\n            opacity: 0\n        },\n        animate: {\n            opacity: 1,\n            transition: {\n                duration: 0.6,\n                ease: [\n                    0.25,\n                    0.46,\n                    0.45,\n                    0.94\n                ]\n            }\n        },\n        className: `${toWidth} mx-auto transition-all duration-700 ease-out ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/full-width/PageTransition.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n_c2 = WidthTransition;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PageTransition\");\n$RefreshReg$(_c1, \"ContentTransition\");\n$RefreshReg$(_c2, \"WidthTransition\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/full-width/PageTransition.tsx\n"));

/***/ })

});