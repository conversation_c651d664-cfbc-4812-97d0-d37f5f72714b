"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/demo/full-width-layout/page",{

/***/ "(app-pages-browser)/./src/components/layout/full-width/PageTransition.tsx":
/*!*************************************************************!*\
  !*** ./src/components/layout/full-width/PageTransition.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContentTransition: function() { return /* binding */ ContentTransition; },\n/* harmony export */   PageTransition: function() { return /* binding */ PageTransition; },\n/* harmony export */   WidthTransition: function() { return /* binding */ WidthTransition; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ PageTransition,ContentTransition,WidthTransition auto */ \n/**\n * 页面过渡动画组件\n * 专为全宽布局优化，确保从列表页到详情页的平滑过渡\n * 临时简化版本，使用CSS动画替代framer-motion\n */ function PageTransition({ children, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `animate-fade-in-up ${className || \"\"}`,\n        style: {\n            animationDuration: \"0.6s\",\n            animationTimingFunction: \"cubic-bezier(0.25, 0.46, 0.45, 0.94)\"\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/full-width/PageTransition.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_c = PageTransition;\n/**\n * 内容区域过渡动画\n * 用于详情页内容的渐进式显示\n * 临时简化版本，使用CSS动画替代framer-motion\n */ function ContentTransition({ children, delay = 0, className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `animate-fade-in-up ${className || \"\"}`,\n        style: {\n            animationDelay: `${delay * 100}ms`,\n            animationDuration: \"0.5s\",\n            animationTimingFunction: \"cubic-bezier(0.25, 0.46, 0.45, 0.94)\",\n            animationFillMode: \"both\"\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/full-width/PageTransition.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ContentTransition;\n/**\n * 宽度过渡动画\n * 专门用于容器宽度的平滑变化\n */ function WidthTransition({ children, fromWidth = \"max-w-5xl\", toWidth = \"max-w-7xl\", className }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(motion.div, {\n        initial: {\n            opacity: 0\n        },\n        animate: {\n            opacity: 1,\n            transition: {\n                duration: 0.6,\n                ease: [\n                    0.25,\n                    0.46,\n                    0.45,\n                    0.94\n                ]\n            }\n        },\n        className: `${toWidth} mx-auto transition-all duration-700 ease-out ${className}`,\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/full-width/PageTransition.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n_c2 = WidthTransition;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PageTransition\");\n$RefreshReg$(_c1, \"ContentTransition\");\n$RefreshReg$(_c2, \"WidthTransition\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2xheW91dC9mdWxsLXdpZHRoL1BhZ2VUcmFuc2l0aW9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQVNBOzs7O0NBSUMsR0FDTSxTQUFTQSxlQUFlLEVBQUVDLFFBQVEsRUFBRUMsU0FBUyxFQUF1QjtJQUN6RSxxQkFDRSw4REFBQ0M7UUFDQ0QsV0FBVyxDQUFDLG1CQUFtQixFQUFFQSxhQUFhLEdBQUcsQ0FBQztRQUNsREUsT0FBTztZQUNMQyxtQkFBbUI7WUFDbkJDLHlCQUF5QjtRQUMzQjtrQkFFQ0w7Ozs7OztBQUdQO0tBWmdCRDtBQWNoQjs7OztDQUlDLEdBQ00sU0FBU08sa0JBQWtCLEVBQ2hDTixRQUFRLEVBQ1JPLFFBQVEsQ0FBQyxFQUNUTixTQUFTLEVBS1Y7SUFDQyxxQkFDRSw4REFBQ0M7UUFDQ0QsV0FBVyxDQUFDLG1CQUFtQixFQUFFQSxhQUFhLEdBQUcsQ0FBQztRQUNsREUsT0FBTztZQUNMSyxnQkFBZ0IsQ0FBQyxFQUFFRCxRQUFRLElBQUksRUFBRSxDQUFDO1lBQ2xDSCxtQkFBbUI7WUFDbkJDLHlCQUF5QjtZQUN6QkksbUJBQW1CO1FBQ3JCO2tCQUVDVDs7Ozs7O0FBR1A7TUF0QmdCTTtBQXdCaEI7OztDQUdDLEdBQ00sU0FBU0ksZ0JBQWdCLEVBQzlCVixRQUFRLEVBQ1JXLFlBQVksV0FBVyxFQUN2QkMsVUFBVSxXQUFXLEVBQ3JCWCxTQUFTLEVBTVY7SUFDQyxxQkFDRSw4REFBQ1ksT0FBT1gsR0FBRztRQUNUWSxTQUFTO1lBQ1BDLFNBQVM7UUFDWDtRQUNBQyxTQUFTO1lBQ1BELFNBQVM7WUFDVEUsWUFBWTtnQkFDVkMsVUFBVTtnQkFDVkMsTUFBTTtvQkFBQztvQkFBTTtvQkFBTTtvQkFBTTtpQkFBSztZQUNoQztRQUNGO1FBQ0FsQixXQUFXLENBQUMsRUFBRVcsUUFBUSw4Q0FBOEMsRUFBRVgsVUFBVSxDQUFDO2tCQUVoRkQ7Ozs7OztBQUdQO01BNUJnQlUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvbGF5b3V0L2Z1bGwtd2lkdGgvUGFnZVRyYW5zaXRpb24udHN4PzE4NzIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0J1xuXG5pbnRlcmZhY2UgUGFnZVRyYW5zaXRpb25Qcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbiAgY2xhc3NOYW1lPzogc3RyaW5nXG59XG5cbi8qKlxuICog6aG16Z2i6L+H5rih5Yqo55S757uE5Lu2XG4gKiDkuJPkuLrlhajlrr3luIPlsYDkvJjljJbvvIznoa7kv53ku47liJfooajpobXliLDor6bmg4XpobXnmoTlubPmu5Hov4fmuKFcbiAqIOS4tOaXtueugOWMlueJiOacrO+8jOS9v+eUqENTU+WKqOeUu+abv+S7o2ZyYW1lci1tb3Rpb25cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIFBhZ2VUcmFuc2l0aW9uKHsgY2hpbGRyZW4sIGNsYXNzTmFtZSB9OiBQYWdlVHJhbnNpdGlvblByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgY2xhc3NOYW1lPXtgYW5pbWF0ZS1mYWRlLWluLXVwICR7Y2xhc3NOYW1lIHx8ICcnfWB9XG4gICAgICBzdHlsZT17e1xuICAgICAgICBhbmltYXRpb25EdXJhdGlvbjogJzAuNnMnLFxuICAgICAgICBhbmltYXRpb25UaW1pbmdGdW5jdGlvbjogJ2N1YmljLWJlemllcigwLjI1LCAwLjQ2LCAwLjQ1LCAwLjk0KSdcbiAgICAgIH19XG4gICAgPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvZGl2PlxuICApXG59XG5cbi8qKlxuICog5YaF5a655Yy65Z+f6L+H5rih5Yqo55S7XG4gKiDnlKjkuo7or6bmg4XpobXlhoXlrrnnmoTmuJDov5vlvI/mmL7npLpcbiAqIOS4tOaXtueugOWMlueJiOacrO+8jOS9v+eUqENTU+WKqOeUu+abv+S7o2ZyYW1lci1tb3Rpb25cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIENvbnRlbnRUcmFuc2l0aW9uKHtcbiAgY2hpbGRyZW4sXG4gIGRlbGF5ID0gMCxcbiAgY2xhc3NOYW1lXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbiAgZGVsYXk/OiBudW1iZXJcbiAgY2xhc3NOYW1lPzogc3RyaW5nXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgY2xhc3NOYW1lPXtgYW5pbWF0ZS1mYWRlLWluLXVwICR7Y2xhc3NOYW1lIHx8ICcnfWB9XG4gICAgICBzdHlsZT17e1xuICAgICAgICBhbmltYXRpb25EZWxheTogYCR7ZGVsYXkgKiAxMDB9bXNgLFxuICAgICAgICBhbmltYXRpb25EdXJhdGlvbjogJzAuNXMnLFxuICAgICAgICBhbmltYXRpb25UaW1pbmdGdW5jdGlvbjogJ2N1YmljLWJlemllcigwLjI1LCAwLjQ2LCAwLjQ1LCAwLjk0KScsXG4gICAgICAgIGFuaW1hdGlvbkZpbGxNb2RlOiAnYm90aCdcbiAgICAgIH19XG4gICAgPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvZGl2PlxuICApXG59XG5cbi8qKlxuICog5a695bqm6L+H5rih5Yqo55S7XG4gKiDkuJPpl6jnlKjkuo7lrrnlmajlrr3luqbnmoTlubPmu5Hlj5jljJZcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIFdpZHRoVHJhbnNpdGlvbih7IFxuICBjaGlsZHJlbiwgXG4gIGZyb21XaWR0aCA9ICdtYXgtdy01eGwnLFxuICB0b1dpZHRoID0gJ21heC13LTd4bCcsXG4gIGNsYXNzTmFtZSBcbn06IHsgXG4gIGNoaWxkcmVuOiBSZWFjdE5vZGVcbiAgZnJvbVdpZHRoPzogc3RyaW5nXG4gIHRvV2lkdGg/OiBzdHJpbmdcbiAgY2xhc3NOYW1lPzogc3RyaW5nIFxufSkge1xuICByZXR1cm4gKFxuICAgIDxtb3Rpb24uZGl2XG4gICAgICBpbml0aWFsPXt7IFxuICAgICAgICBvcGFjaXR5OiAwLFxuICAgICAgfX1cbiAgICAgIGFuaW1hdGU9e3sgXG4gICAgICAgIG9wYWNpdHk6IDEsXG4gICAgICAgIHRyYW5zaXRpb246IHtcbiAgICAgICAgICBkdXJhdGlvbjogMC42LFxuICAgICAgICAgIGVhc2U6IFswLjI1LCAwLjQ2LCAwLjQ1LCAwLjk0XVxuICAgICAgICB9XG4gICAgICB9fVxuICAgICAgY2xhc3NOYW1lPXtgJHt0b1dpZHRofSBteC1hdXRvIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTcwMCBlYXNlLW91dCAke2NsYXNzTmFtZX1gfVxuICAgID5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L21vdGlvbi5kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJQYWdlVHJhbnNpdGlvbiIsImNoaWxkcmVuIiwiY2xhc3NOYW1lIiwiZGl2Iiwic3R5bGUiLCJhbmltYXRpb25EdXJhdGlvbiIsImFuaW1hdGlvblRpbWluZ0Z1bmN0aW9uIiwiQ29udGVudFRyYW5zaXRpb24iLCJkZWxheSIsImFuaW1hdGlvbkRlbGF5IiwiYW5pbWF0aW9uRmlsbE1vZGUiLCJXaWR0aFRyYW5zaXRpb24iLCJmcm9tV2lkdGgiLCJ0b1dpZHRoIiwibW90aW9uIiwiaW5pdGlhbCIsIm9wYWNpdHkiLCJhbmltYXRlIiwidHJhbnNpdGlvbiIsImR1cmF0aW9uIiwiZWFzZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/full-width/PageTransition.tsx\n"));

/***/ })

});