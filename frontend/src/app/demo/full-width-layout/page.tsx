import { Metadata } from 'next'
import { 
  FullWidthContainer, 
  ContentContainer, 
  SectionContainer,
  PageTransition,
  ContentTransition 
} from '@/components/layout/full-width'
import { Prose } from '@/components/shared/Prose'
import { ResponsiveTableOfContents } from '@/components/blog/ResponsiveTableOfContents'

export const metadata: Metadata = {
  title: '全宽布局系统演示 | 红点奖级设计',
  description: '展示渐进式全宽布局系统的设计效果，从传统居中布局到现代化全宽体验的完美过渡',
}

const demoContent = `
# 全宽布局系统演示

这是一个展示全新全宽布局系统的演示页面。我们采用了**渐进式扩展**的设计理念，确保在不同屏幕尺寸下都能提供最佳的阅读体验。

## 设计理念

### 渐进式扩展
我们的布局系统不是简单的全宽铺开，而是根据屏幕尺寸智能调整：

- **小屏设备** (< 640px): 保持传统的紧凑布局
- **中等屏幕** (640px - 1024px): 适度扩展，提升视觉舒适度  
- **桌面显示器** (1024px - 1920px): 充分利用屏幕空间
- **超宽显示器** (> 1920px): 限制过宽，保持可读性

### 阅读体验优化

我们特别注重文本的可读性：

1. **行长度控制**: 每行保持在65-75个字符，符合人眼阅读习惯
2. **动态间距**: 根据屏幕尺寸调整段落和元素间距
3. **视觉层次**: 清晰的标题层级和内容分组

## 技术实现

### 核心组件

\`\`\`tsx
// FullWidthContainer - 全宽容器
<FullWidthContainer 
  variant="constrained" 
  maxWidth="5xl" 
  padding="md"
>
  {/* 内容 */}
</FullWidthContainer>

// ContentContainer - 内容容器
<ContentContainer 
  contentType="article" 
  responsive="progressive"
  readingOptimized={true}
>
  {/* 文章内容 */}
</ContentContainer>
\`\`\`

### 响应式断点

我们扩展了Tailwind的断点系统：

- \`xs\`: 320px (小型手机)
- \`sm\`: 640px (大型手机) 
- \`md\`: 768px (平板竖屏)
- \`lg\`: 1024px (平板横屏/小型笔记本)
- \`xl\`: 1280px (桌面显示器)
- \`2xl\`: 1536px (大型显示器)
- \`3xl\`: 1920px (超宽显示器)
- \`4xl\`: 2560px (4K显示器)
- \`5xl\`: 3840px (8K显示器)

## 视觉效果

### 动画过渡

页面采用了精心设计的过渡动画：

- **页面进入**: 从窄到宽的扩展效果，模拟从列表页到详情页的自然过渡
- **内容显示**: 分层渐进显示，避免突兀的布局跳跃
- **交互反馈**: 悬停和点击都有流畅的视觉反馈

### 3D效果

我们在关键元素上应用了微妙的3D效果：

- **卡片阴影**: 多层次阴影营造深度感
- **悬停提升**: 鼠标悬停时元素轻微上浮
- **透视变换**: 使用CSS 3D变换增强立体感

## 兼容性保证

### 向后兼容

新的布局系统完全向后兼容：

- 现有组件无需修改即可使用
- 渐进式增强，不影响旧版本功能
- 优雅降级，在不支持的环境中回退到标准布局

### 性能优化

- **懒加载**: 非关键内容延迟加载
- **代码分割**: 按需加载组件代码
- **缓存策略**: 智能缓存静态资源

## 用户体验提升

### 阅读体验

1. **更宽的视野**: 在大屏幕上提供更宽的阅读区域
2. **更好的层次**: 清晰的内容分组和视觉层次
3. **更流畅的导航**: 优化的目录导航和页面跳转

### 交互体验

1. **平滑过渡**: 页面间的无缝切换
2. **响应式交互**: 适配不同设备的交互方式
3. **视觉反馈**: 及时的操作反馈

## 设计标准

这个布局系统的设计标准参考了国际设计奖项的要求：

- **创新性**: 突破传统布局限制
- **实用性**: 真正提升用户体验
- **美观性**: 符合现代审美标准
- **技术性**: 采用先进的前端技术

## 总结

全宽布局系统代表了我们对现代网页设计的理解和追求。它不仅仅是技术的升级，更是用户体验的革新。

通过渐进式扩展、智能响应式设计和精心打磨的视觉效果，我们创造了一个既美观又实用的布局系统，为用户提供了前所未有的阅读和浏览体验。

---

*这个演示页面展示了全宽布局系统的核心特性。在实际使用中，系统会根据内容类型和用户设备自动优化显示效果。*
`

export default function FullWidthLayoutDemo() {
  return (
    <PageTransition>
      <div className="relative min-h-screen">
        {/* 全宽布局容器 */}
        <FullWidthContainer 
          variant="constrained" 
          maxWidth="5xl" 
          padding="md"
          className="mt-16 lg:mt-32"
        >
          <div className="xl:relative">
            
            {/* 主内容区域 - 渐进式全宽布局 */}
            <ContentContainer 
              contentType="article" 
              responsive="progressive"
              spacing="none"
              className="min-h-0"
            >
              {/* 页面头部 */}
              <ContentTransition delay={0.1}>
                <SectionContainer 
                  section="header" 
                  layout="contained" 
                  spacing="lg"
                  background="subtle"
                  className="mb-12 rounded-3xl border border-border/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-500 overflow-hidden"
                >
                  <div className="text-center">
                    <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent mb-4">
                      全宽布局系统演示
                    </h1>
                    <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                      体验从传统居中布局到现代化全宽设计的完美过渡，感受红点奖级别的设计标准
                    </p>
                  </div>
                </SectionContainer>
              </ContentTransition>

              {/* 文章内容 */}
              <ContentTransition delay={0.2}>
                <SectionContainer 
                  section="content" 
                  layout="contained" 
                  spacing="lg"
                  className="article-content" 
                  data-mdx-content
                >
                  <Prose className="mt-8 mb-0">
                    <div className="space-y-6">
                      <h1 id="title">全宽布局系统演示</h1>
                      <p>这是一个展示全新全宽布局系统的演示页面。我们采用了<strong>渐进式扩展</strong>的设计理念，确保在不同屏幕尺寸下都能提供最佳的阅读体验。</p>

                      <h2 id="design-concept">设计理念</h2>

                      <h3 id="progressive-expansion">渐进式扩展</h3>
                      <p>我们的布局系统不是简单的全宽铺开，而是根据屏幕尺寸智能调整：</p>
                      <ul>
                        <li><strong>小屏设备</strong> (&lt; 640px): 保持传统的紧凑布局</li>
                        <li><strong>中等屏幕</strong> (640px - 1024px): 适度扩展，提升视觉舒适度</li>
                        <li><strong>桌面显示器</strong> (1024px - 1920px): 充分利用屏幕空间</li>
                        <li><strong>超宽显示器</strong> (&gt; 1920px): 限制过宽，保持可读性</li>
                      </ul>

                      <h3 id="reading-optimization">阅读体验优化</h3>
                      <p>我们特别注重文本的可读性：</p>
                      <ol>
                        <li><strong>行长度控制</strong>: 每行保持在65-75个字符，符合人眼阅读习惯</li>
                        <li><strong>动态间距</strong>: 根据屏幕尺寸调整段落和元素间距</li>
                        <li><strong>视觉层次</strong>: 清晰的标题层级和内容分组</li>
                      </ol>

                      <h2 id="technical-implementation">技术实现</h2>

                      <h3 id="core-components">核心组件</h3>
                      <pre><code>{`// FullWidthContainer - 全宽容器
<FullWidthContainer
  variant="constrained"
  maxWidth="5xl"
  padding="md"
>
  {/* 内容 */}
</FullWidthContainer>

// ContentContainer - 内容容器
<ContentContainer
  contentType="article"
  responsive="progressive"
  readingOptimized={true}
>
  {/* 文章内容 */}
</ContentContainer>`}</code></pre>

                      <h3 id="responsive-breakpoints">响应式断点</h3>
                      <p>我们扩展了Tailwind的断点系统：</p>
                      <ul>
                        <li><code>xs</code>: 320px (小型手机)</li>
                        <li><code>sm</code>: 640px (大型手机)</li>
                        <li><code>md</code>: 768px (平板竖屏)</li>
                        <li><code>lg</code>: 1024px (平板横屏/小型笔记本)</li>
                        <li><code>xl</code>: 1280px (桌面显示器)</li>
                        <li><code>2xl</code>: 1536px (大型显示器)</li>
                        <li><code>3xl</code>: 1920px (超宽显示器)</li>
                        <li><code>4xl</code>: 2560px (4K显示器)</li>
                        <li><code>5xl</code>: 3840px (8K显示器)</li>
                      </ul>

                      <h2 id="visual-effects">视觉效果</h2>

                      <h3 id="animation-transitions">动画过渡</h3>
                      <p>页面采用了精心设计的过渡动画：</p>
                      <ul>
                        <li><strong>页面进入</strong>: 从窄到宽的扩展效果，模拟从列表页到详情页的自然过渡</li>
                        <li><strong>内容显示</strong>: 分层渐进显示，避免突兀的布局跳跃</li>
                        <li><strong>交互反馈</strong>: 悬停和点击都有流畅的视觉反馈</li>
                      </ul>

                      <h3 id="3d-effects">3D效果</h3>
                      <p>我们在关键元素上应用了微妙的3D效果：</p>
                      <ul>
                        <li><strong>卡片阴影</strong>: 多层次阴影营造深度感</li>
                        <li><strong>悬停提升</strong>: 鼠标悬停时元素轻微上浮</li>
                        <li><strong>透视变换</strong>: 使用CSS 3D变换增强立体感</li>
                      </ul>

                      <h2 id="compatibility">兼容性保证</h2>

                      <h3 id="backward-compatibility">向后兼容</h3>
                      <p>新的布局系统完全向后兼容：</p>
                      <ul>
                        <li>现有组件无需修改即可使用</li>
                        <li>渐进式增强，不影响旧版本功能</li>
                        <li>优雅降级，在不支持的环境中回退到标准布局</li>
                      </ul>

                      <h3 id="performance-optimization">性能优化</h3>
                      <ul>
                        <li><strong>懒加载</strong>: 非关键内容延迟加载</li>
                        <li><strong>代码分割</strong>: 按需加载组件代码</li>
                        <li><strong>缓存策略</strong>: 智能缓存静态资源</li>
                      </ul>

                      <h2 id="user-experience">用户体验提升</h2>

                      <h3 id="reading-experience">阅读体验</h3>
                      <ol>
                        <li><strong>更宽的视野</strong>: 在大屏幕上提供更宽的阅读区域</li>
                        <li><strong>更好的层次</strong>: 清晰的内容分组和视觉层次</li>
                        <li><strong>更流畅的导航</strong>: 优化的目录导航和页面跳转</li>
                      </ol>

                      <h3 id="interaction-experience">交互体验</h3>
                      <ol>
                        <li><strong>平滑过渡</strong>: 页面间的无缝切换</li>
                        <li><strong>响应式交互</strong>: 适配不同设备的交互方式</li>
                        <li><strong>视觉反馈</strong>: 及时的操作反馈</li>
                      </ol>

                      <h2 id="design-standards">设计标准</h2>
                      <p>这个布局系统的设计标准参考了国际设计奖项的要求：</p>
                      <ul>
                        <li><strong>创新性</strong>: 突破传统布局限制</li>
                        <li><strong>实用性</strong>: 真正提升用户体验</li>
                        <li><strong>美观性</strong>: 符合现代审美标准</li>
                        <li><strong>技术性</strong>: 采用先进的前端技术</li>
                      </ul>

                      <h2 id="conclusion">总结</h2>
                      <p>全宽布局系统代表了我们对现代网页设计的理解和追求。它不仅仅是技术的升级，更是用户体验的革新。</p>
                      <p>通过渐进式扩展、智能响应式设计和精心打磨的视觉效果，我们创造了一个既美观又实用的布局系统，为用户提供了前所未有的阅读和浏览体验。</p>

                      <hr />
                      <p><em>这个演示页面展示了全宽布局系统的核心特性。在实际使用中，系统会根据内容类型和用户设备自动优化显示效果。</em></p>
                    </div>
                  </Prose>
                </SectionContainer>
              </ContentTransition>
            </ContentContainer>

            {/* 目录导航 */}
            <div className="hidden lg:block fixed right-4 top-1/2 transform -translate-y-1/2 w-80 z-40 pointer-events-auto">
              <ResponsiveTableOfContents content={demoContent} />
            </div>

            {/* 移动端目录导航 */}
            <div className="md:hidden">
              <ResponsiveTableOfContents content={demoContent} />
            </div>
          </div>
        </FullWidthContainer>
      </div>
    </PageTransition>
  )
}
