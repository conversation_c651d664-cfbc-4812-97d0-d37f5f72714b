import clsx from 'clsx'

export function Prose({
  className,
  ...props
}: React.ComponentPropsWithoutRef<'div'>) {
  return (
    <div
      className={clsx(
        className,
        'prose prose-lg dark:prose-invert rich-text-content',
        // 优化的响应式字体和间距设置
        'prose-headings:font-semibold prose-h1:text-4xl prose-h2:text-3xl prose-h3:text-2xl prose-h4:text-xl prose-h5:text-lg prose-h6:text-base',
        'prose-p:my-3 prose-p:leading-relaxed prose-p:text-lg sm:prose-p:text-xl',
        'prose-a:text-primary prose-a:font-semibold prose-a:decoration-1 prose-a:underline-offset-2 hover:prose-a:decoration-2',
        'prose-strong:font-semibold prose-strong:text-gray-900 dark:prose-strong:text-gray-100',
        'prose-ul:list-disc prose-ul:pl-6 prose-ol:list-decimal prose-ol:pl-6',
        'prose-li:my-1.5 prose-li:pl-2 prose-li:text-lg sm:prose-li:text-xl prose-li:leading-relaxed',
        'prose-blockquote:border-l-4 prose-blockquote:border-gray-300 dark:prose-blockquote:border-gray-700 prose-blockquote:pl-6 prose-blockquote:italic prose-blockquote:text-lg prose-blockquote:my-4',
        'prose-img:rounded-lg prose-img:mx-auto prose-img:shadow-lg',
        'prose-code:px-2 prose-code:py-1 prose-code:rounded-md prose-code:bg-gray-100 dark:prose-code:bg-gray-800 prose-code:text-base prose-code:font-medium',
        'prose-pre:bg-gray-100 dark:prose-pre:bg-gray-800 prose-pre:rounded-lg prose-pre:p-6 prose-pre:overflow-auto prose-pre:text-base prose-pre:leading-relaxed',
        // 表格样式 - 优化信息密度
        'prose-table:border-collapse prose-table:w-full prose-table:text-base prose-table:my-6',
        'prose-thead:bg-gray-50 dark:prose-thead:bg-gray-800',
        'prose-th:p-3 prose-th:border prose-th:border-gray-200 dark:prose-th:border-gray-700 prose-th:font-semibold prose-th:text-left',
        'prose-td:p-3 prose-td:border prose-td:border-gray-200 dark:prose-td:border-gray-700 prose-td:text-base',
        // 文件卡片样式
        '[&_.project-card]:my-4 [&_.project-card]:bg-white [&_.project-card]:dark:bg-zinc-800 [&_.project-card]:border [&_.project-card]:border-zinc-200 [&_.project-card]:dark:border-zinc-700 [&_.project-card]:rounded-lg [&_.project-card]:overflow-hidden [&_.project-card]:transition-all [&_.project-card]:duration-300',
        '[&_.project-card:hover]:transform [&_.project-card:hover]:-translate-y-1 [&_.project-card:hover]:shadow-lg',
        '[&_.project-card-content]:p-6',
        '[&_.project-card-title]:text-xl [&_.project-card-title]:font-semibold [&_.project-card-title]:text-zinc-900 [&_.project-card-title]:dark:text-zinc-100 [&_.project-card-title]:mb-2',
        '[&_.project-card-desc]:text-sm [&_.project-card-desc]:text-zinc-600 [&_.project-card-desc]:dark:text-zinc-400',
        '[&_.project-card-link]:inline-flex [&_.project-card-link]:items-center [&_.project-card-link]:text-primary [&_.project-card-link]:text-sm [&_.project-card-link]:font-medium [&_.project-card-link]:no-underline [&_.project-card-link:hover]:text-primary-dark',
        '[&_.project-card-link-text]:mr-2',
        '[&_.project-card-link-icon]:w-4 [&_.project-card-link-icon]:h-4',
        // 全宽布局优化 - 渐进式宽度扩展
        'max-w-none',
        // 在全宽布局下的阅读优化
        'prose-p:max-w-[65ch] prose-li:max-w-[65ch]', // 限制段落和列表项的行长度
        'prose-headings:max-w-none', // 标题可以全宽
        'prose-blockquote:max-w-[60ch]', // 引用块适中宽度
        'prose-pre:max-w-none prose-table:max-w-none prose-img:max-w-none', // 代码、表格、图片可以全宽
      )}
      style={{
        columnCount: 'unset',
        columns: 'unset',
        columnFill: 'unset',
        columnGap: 'unset'
      }}
      {...props}
    />
  )
}
