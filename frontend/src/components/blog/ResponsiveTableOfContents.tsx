'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { cn } from '@/lib/utils'
import { Search, ChevronDown, ChevronUp, BookOpen, Eye, Target, Circle } from 'lucide-react'

interface TocItem {
  id: string
  text: string
  level: number
  children?: TocItem[]
  preview?: string // 章节预览内容
}

interface ResponsiveTableOfContentsProps {
  content: string
  className?: string
}

/**
 * 响应式目录导航组件
 * 根据屏幕尺寸自动调整布局和显示方式
 */
export function ResponsiveTableOfContents({ content, className }: ResponsiveTableOfContentsProps) {
  const [tocItems, setTocItems] = useState<TocItem[]>([])
  const [activeId, setActiveId] = useState<string>('')
  const [isVisible, setIsVisible] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileOpen, setIsMobileOpen] = useState(false)
  const [isDesktop, setIsDesktop] = useState(false)
  const [readingProgress, setReadingProgress] = useState(0)
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)
  const [previewContent, setPreviewContent] = useState<string>('')
  const [contentAreaBounds, setContentAreaBounds] = useState({ right: 0, left: 0 })
  const tocRef = useRef<HTMLDivElement>(null)

  // 解析内容生成目录 - 增强版本
  useEffect(() => {
    const extractHeadings = () => {
      // 优先使用文章内容区域，避免识别评论区域的标题
      let proseElement = document.querySelector('.article-content[data-mdx-content]')
      if (!proseElement) {
        // 兼容旧版本，但排除评论区域
        proseElement = document.querySelector('[data-mdx-content]')
      }
      if (!proseElement) return

      const headings = proseElement.querySelectorAll('h1, h2, h3, h4, h5, h6')

      const items: TocItem[] = []
      headings.forEach((heading, index) => {
        // 如果使用兼容模式，排除评论区域的标题
        if (!proseElement?.classList.contains('article-content')) {
          const commentSection = heading.closest('[data-comment-section]')
          if (commentSection) return
        }

        const id = heading.id
        const text = heading.textContent || ''
        const level = parseInt(heading.tagName.charAt(1))

        if (id && text) {
          // 提取章节预览内容
          let preview = ''

          // 查找下一个标题之前的内容作为预览
          const nextHeading = Array.from(headings)[index + 1]
          let currentElement = heading.nextElementSibling

          while (currentElement && currentElement !== nextHeading && preview.length < 150) {
            const textContent = currentElement.textContent || ''
            if (textContent.trim()) {
              preview += textContent.trim() + ' '
            }
            currentElement = currentElement.nextElementSibling
          }

          // 截断预览内容
          if (preview.length > 150) {
            preview = preview.substring(0, 150) + '...'
          }

          items.push({
            id,
            text,
            level,
            preview: preview.trim()
          })
        }
      })

      setTocItems(items)
      setIsVisible(items.length > 1)
    }

    // 立即尝试提取
    extractHeadings()

    // 延迟执行，确保DOM已经渲染
    const timer = setTimeout(extractHeadings, 500)

    // 监听DOM变化
    const observer = new MutationObserver(() => {
      extractHeadings()
    })

    const articleElement = document.querySelector('article')
    if (articleElement) {
      observer.observe(articleElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['id']
      })
    }

    // 监听自定义刷新事件
    const handleTocRefresh = () => {
      extractHeadings()
    }

    window.addEventListener('tocRefresh', handleTocRefresh)

    return () => {
      clearTimeout(timer)
      observer.disconnect()
      window.removeEventListener('tocRefresh', handleTocRefresh)
    }
  }, [content])

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsDesktop(window.innerWidth >= 1024) // 降低到 lg 断点 (1024px)
      
      // 计算内容区域的边界 - 适配全宽布局系统
      let contentElement = document.querySelector('.article-content[data-mdx-content]')
      if (!contentElement) {
        contentElement = document.querySelector('[data-mdx-content]')
      }
      if (!contentElement) {
        // 回退到ContentContainer
        contentElement = document.querySelector('[class*="ContentContainer"]')
      }
      if (contentElement) {
        const rect = contentElement.getBoundingClientRect()
        setContentAreaBounds({
          right: rect.right,
          left: rect.left
        })
      }
    }

    // 初始设置
    handleResize()

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // 监听滚动，更新活跃项
  useEffect(() => {
    if (tocItems.length === 0) return

    const handleScroll = () => {
      // 更新内容区域边界（考虑滚动影响）- 适配全宽布局系统
      let contentElement = document.querySelector('.article-content[data-mdx-content]')
      if (!contentElement) {
        contentElement = document.querySelector('[data-mdx-content]')
      }
      if (!contentElement) {
        // 回退到ContentContainer
        contentElement = document.querySelector('[class*="ContentContainer"]')
      }
      if (!contentElement) return

      const contentRect = contentElement.getBoundingClientRect()
      const scrollTop = window.scrollY

      setContentAreaBounds({
        right: contentRect.right,
        left: contentRect.left
      })

      // 计算基于文章实际内容的阅读进度
      const contentTop = contentRect.top + scrollTop

      // 找到文章内容的最后一个实际元素（排除空白区域）
      const lastContentElement = Array.from(contentElement.children).pop()
      let contentBottom = contentRect.bottom + scrollTop

      if (lastContentElement) {
        const lastElementRect = lastContentElement.getBoundingClientRect()
        contentBottom = lastElementRect.bottom + scrollTop
      }

      const contentHeight = contentBottom - contentTop
      const viewportTop = scrollTop + 200 // 考虑header高度
      
      let progress = 0
      if (contentHeight > 0) {
        // 当视窗顶部到达或超过内容底部时，进度应该是100%
        if (viewportTop >= contentBottom - 100) { // 留100px缓冲区
          progress = 100
        } else {
          progress = Math.min(100, Math.max(0, ((viewportTop - contentTop) / contentHeight) * 100))
        }
      }
      setReadingProgress(progress)

      const headings = tocItems.map(item => document.getElementById(item.id)).filter(Boolean)
      let currentActiveId = ''

      // 改进的活跃状态检测：确保只有在文章内容区域内的标题才会被激活
      for (let i = 0; i < headings.length; i++) {
        const heading = headings[i]
        if (heading) {
          const headingRect = heading.getBoundingClientRect()
          const headingTop = headingRect.top + scrollTop
          
          // 检查标题是否在文章实际内容区域内
          if (headingTop >= contentTop && headingTop <= contentBottom) {
            // 标题距离视窗顶部的距离
            const distanceFromTop = headingRect.top
            
            // 只有当标题在合理的视窗位置时才认为是活跃的
            if (distanceFromTop <= 300) { // 标题在视窗顶部300px内
              currentActiveId = heading.id
            }
          }
        }
      }

      // 如果没有找到活跃标题，使用最后一个在文章内容区域内且已经滚过的标题
      if (!currentActiveId) {
        for (let i = headings.length - 1; i >= 0; i--) {
          const heading = headings[i]
          if (heading) {
            const headingRect = heading.getBoundingClientRect()
            const headingTop = headingRect.top + scrollTop
            
            if (headingTop >= proseTop && headingTop <= contentBottom && headingRect.top <= 300) {
              currentActiveId = heading.id
              break
            }
          }
        }
      }

      // 如果还是没有找到，并且用户在文章开始部分，使用第一个标题
      if (!currentActiveId && scrollTop + 200 < proseTop + 500 && headings.length > 0) {
        currentActiveId = headings[0]?.id || ''
      }

      setActiveId(currentActiveId)
    }

    // 使用节流优化性能
    let ticking = false
    const throttledHandleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll()
          ticking = false
        })
        ticking = true
      }
    }

    // 初始设置活跃项
    handleScroll()

    window.addEventListener('scroll', throttledHandleScroll, { passive: true })
    return () => window.removeEventListener('scroll', throttledHandleScroll)
  }, [tocItems])

  // 滚动到指定标题
  const scrollToHeading = useCallback((id: string) => {
    const element = document.getElementById(id)
    if (element) {
      // 计算更合适的偏移量
      const headerHeight = 80 // 估算的头部高度
      const extraPadding = 100 // 额外的视觉间距
      const yOffset = -(headerHeight + extraPadding)

      const elementRect = element.getBoundingClientRect()
      const elementTop = elementRect.top + window.pageYOffset
      let targetY = elementTop + yOffset

      // 处理页面末尾的章节 - 确保不会滚动超出页面底部
      const documentHeight = document.documentElement.scrollHeight
      const windowHeight = window.innerHeight
      const maxScrollY = documentHeight - windowHeight

      // 如果目标位置超出了页面底部，调整到页面底部
      if (targetY > maxScrollY) {
        targetY = Math.max(maxScrollY, 0)
      }

      // 确保不会滚动到负值
      targetY = Math.max(targetY, 0)

      window.scrollTo({
        top: targetY,
        behavior: 'smooth'
      })
      setIsMobileOpen(false) // 移动端点击后关闭
    }
  }, [])

  // 过滤搜索结果
  const filteredItems = tocItems.filter(item =>
    item.text.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // 圆形进度指示器组件
  const CircularProgress = ({ progress }: { progress: number }) => {
    const radius = 20
    const circumference = 2 * Math.PI * radius
    const strokeDashoffset = circumference - (progress / 100) * circumference

    return (
      <div className="relative w-12 h-12 group/progress">
        <svg className="w-12 h-12 transform -rotate-90" viewBox="0 0 48 48">
          {/* 背景圆环 */}
          <circle
            cx="24"
            cy="24"
            r={radius}
            stroke="hsl(var(--muted))"
            strokeWidth="3"
            fill="none"
            className="opacity-20"
          />
          {/* 进度圆环 */}
          <circle
            cx="24"
            cy="24"
            r={radius}
            stroke="hsl(var(--primary))"
            strokeWidth="3"
            fill="none"
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
            className="transition-all duration-300 ease-out drop-shadow-sm"
            strokeLinecap="round"
          />
        </svg>

        {/* 中心文字 */}
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-xs font-semibold text-primary group-hover/progress:scale-110 transition-transform duration-200">
            {Math.round(progress)}%
          </span>
        </div>

        {/* 悬停光环 */}
        <div className="absolute inset-0 rounded-full bg-primary/10 opacity-0 group-hover/progress:opacity-100 transition-opacity duration-300 scale-110" />
      </div>
    )
  }

  // 增强的目录项渲染 - 3D效果和章节预览
  const renderTocItem = (item: TocItem) => {
    const isActive = activeId === item.id
    const isHovered = hoveredItem === item.id
    const paddingLeft = (item.level - 1) * 12 + 8

    return (
      <div key={item.id} className="relative group/item">
        <button
          onClick={() => scrollToHeading(item.id)}
          onMouseEnter={() => {
            setHoveredItem(item.id)
            setPreviewContent(item.preview || '')
          }}
          onMouseLeave={() => {
            setHoveredItem(null)
            setPreviewContent('')
          }}
          className={cn(
            "w-full text-left text-sm transition-all duration-300 py-3 px-3 rounded-lg hover:bg-muted/50 group relative overflow-hidden hover:scale-105 hover:-translate-y-0.5 hover:shadow-lg",
            {
              'text-primary font-medium bg-gradient-to-r from-primary/15 to-primary/5 border-l-4 border-primary shadow-md scale-105': isActive,
              'text-muted-foreground hover:text-foreground': !isActive,
            }
          )}
          style={{
            paddingLeft: `${paddingLeft}px`,
            transform: 'perspective(200px) translateZ(0)',
            transformStyle: 'preserve-3d'
          }}
        >
          {/* 3D背景效果 */}
          <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

          {/* 活跃状态指示器 */}
          {isActive && (
            <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-primary via-primary/80 to-primary/60 rounded-r-sm shadow-sm" />
          )}

          {/* 标题内容 */}
          <div className="relative z-10 space-y-1">
            <span className="block group-hover:text-foreground transition-colors font-medium">
              {item.text}
            </span>


          </div>

          {/* 悬停光效 */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700" />
        </button>

        {/* 章节预览悬浮卡片 */}
        {isHovered && item.preview && (
          <div
            className="absolute left-full top-0 ml-4 w-80 p-4 bg-card border border-border/50 rounded-xl shadow-xl backdrop-blur-sm z-50 animate-fade-in-up"
            style={{
              transform: 'perspective(300px) translateZ(20px)',
              transformStyle: 'preserve-3d'
            }}
          >
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm font-medium text-primary">
                <BookOpen className="w-4 h-4" />
                <span>章节预览</span>
              </div>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {item.preview}
              </p>
            </div>

            {/* 箭头指示器 */}
            <div className="absolute left-0 top-4 transform -translate-x-1 w-2 h-2 bg-card border-l border-t border-border/50 rotate-45" />
          </div>
        )}
      </div>
    )
  }

  if (!isVisible) return null

  // 桌面端显示 - 增强3D效果，适配全宽布局
  if (isDesktop) {
    return (
      <div className={cn(
        "transition-all duration-700 ease-in-out transform-gpu",
        isCollapsed
          ? "w-16" // 折叠时宽度调整
          : "w-full max-h-[80vh]"
      )}
      style={{
        position: 'relative', // 使用相对定位，适配全宽布局
        // 在全宽布局下，确保目录不会被内容遮挡
        zIndex: 40
      }}>
        <div
          className={cn(
            "bg-card/95 backdrop-blur-md border border-border/50 shadow-xl hover:shadow-2xl group/toc overflow-hidden flex flex-col transform-gpu",
            isCollapsed 
              ? "rounded-full w-16 h-16 items-center justify-center cursor-pointer hover:scale-110 hover:shadow-primary/20 absolute transition-all duration-500 ease-out" 
              : "rounded-2xl max-h-[80vh] relative transition-all duration-500 ease-out"
          )}
          style={isCollapsed ? {
            transform: 'perspective(1000px) translateZ(0) translateX(calc(100% - 2rem))',
            transformStyle: 'preserve-3d',
            willChange: 'transform',
            right: '-2rem',
            width: '4rem',
            height: '4rem'
          } : {
            transform: 'perspective(1000px) translateZ(0)',
            transformStyle: 'preserve-3d',
            willChange: 'transform',
            width: '100%',
            height: 'auto'
          }}
          ref={tocRef}
          onClick={isCollapsed ? () => setIsCollapsed(false) : undefined}
        >
          {/* 3D背景效果 */}
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-blue-500/5 opacity-0 group-hover/toc:opacity-100 transition-opacity duration-500 rounded-inherit" />

          {/* 折叠状态内容 */}
          <div className={cn("absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300", isCollapsed && "opacity-100")}>
            <div className="transform transition-all duration-500 ease-out hover:scale-105">
              <CircularProgress progress={readingProgress} />
            </div>
            {/* 悬停提示 - 调整位置，避免被边缘遮挡 */}
            <div className="absolute -top-12 -left-16 bg-card border rounded-lg px-3 py-1 text-xs whitespace-nowrap opacity-0 group-hover/toc:opacity-100 transition-all duration-300 pointer-events-none z-50 shadow-lg">
              <div className="text-center">
                <div className="font-medium">目录导航</div>
                <div className="text-muted-foreground">{Math.round(readingProgress)}% 已读</div>
              </div>
              {/* 箭头指向圆球 */}
              <div className="absolute top-1/2 -right-1 transform -translate-y-1/2 w-2 h-2 bg-card border-r border-b border-border/50 rotate-45"></div>
            </div>
          </div>

          {/* 展开状态内容 */}
          <div className={cn("opacity-0 transition-opacity duration-300", !isCollapsed && "opacity-100")}>
            {!isCollapsed && (
            <>
              {/* 头部 - 增强设计 */}
              <div className="relative flex items-center justify-between p-4 border-b border-border/30">
                <div className="flex items-center gap-3">
                  <CircularProgress progress={readingProgress} />
                  <div>
                    <h3 className="text-sm font-semibold text-foreground">目录导航</h3>
                    <p className="text-xs text-muted-foreground">
                      {filteredItems.length} 个章节
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setIsCollapsed(!isCollapsed)}
                  className="p-2 rounded-lg hover:bg-muted/50 transition-all duration-300 hover:scale-110 group/btn"
                  style={{
                    transform: 'perspective(100px) translateZ(5px)',
                    transformStyle: 'preserve-3d'
                  }}
                >
                  <ChevronUp className="w-4 h-4 group-hover/btn:scale-110 transition-transform duration-300" />
                </button>
              </div>

              <div className="relative z-10 flex flex-col flex-1 min-h-0">
                {/* 搜索框 - 增强设计 */}
                <div className="p-4 pb-2 flex-shrink-0">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                    <input
                      type="text"
                      placeholder="搜索章节..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 text-sm border border-border/50 rounded-lg bg-background/50 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/30 transition-all duration-200"
                    />
                  </div>
                </div>

                {/* 目录内容 - 增强滚动，适应固定定位 */}
                <div className="flex-1 overflow-y-auto space-y-2 px-4 pb-4 custom-scrollbar min-h-0">
                  {filteredItems.length > 0 ? (
                    filteredItems.map(renderTocItem)
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">未找到匹配的章节</p>
                    </div>
                  )}
                </div>
              </div>
            </>
            )}
          </div>
        </div>
      </div>
    )
  }

  // 移动端显示 (md以下)  
  return (
    <div className="md:hidden">
      {/* 浮动按钮 */}
      <button
        onClick={() => setIsMobileOpen(true)}
        className="fixed bottom-6 right-6 z-40 bg-primary text-primary-foreground p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
      >
        📋
      </button>

      {/* 抽屉遮罩 */}
      {isMobileOpen && (
        <div
          className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm"
          onClick={() => setIsMobileOpen(false)}
        />
      )}

      {/* 抽屉内容 */}
      <div className={cn(
        "fixed bottom-0 left-0 right-0 z-50 bg-card border-t rounded-t-xl transition-transform duration-300",
        isMobileOpen ? "translate-y-0" : "translate-y-full"
      )}>
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">目录导航</h3>
            <button
              onClick={() => setIsMobileOpen(false)}
              className="p-2 rounded-md hover:bg-muted/50"
            >
              ✕
            </button>
          </div>

          {/* 搜索框 */}
          <div className="mb-3">
            <input
              type="text"
              placeholder="搜索章节..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-3 py-2 text-sm border border-muted rounded-md bg-background/50 focus:outline-none focus:ring-2 focus:ring-primary/20"
            />
          </div>

          <div className="max-h-80 overflow-y-auto space-y-2 custom-scrollbar">
            {filteredItems.map(renderTocItem)}
          </div>
        </div>
      </div>
    </div>
  )
}
