'use client'

import { useContext } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

import { AppContext } from '@/app/providers'
import { Container } from '@/components/layout/Container'
import { Prose } from '@/components/shared/Prose'
import { formatDate } from '@/lib/formatDate'
import { ResponsiveTableOfContents } from '@/components/blog/ResponsiveTableOfContents'
import { CommentStats } from '@/components/comment/CommentStats'
import { PageViewCount } from '@/components/comment/PageViewCount'
import { tagStyleGenerator } from '@/lib/colorSystem'
import { Calendar, Eye, Heart, User, Star, BookOpen } from 'lucide-react'
import { cn } from '@/lib/utils'
import React from 'react'

// 导入全宽布局系统
import {
  FullWidthContainer,
  ContentContainer,
  SectionContainer,
  PageTransition,
  ContentTransition
} from '@/components/layout/full-width'

interface WebsiteVersion {
  id: number
  version: string
  title: string
  content: string
  release_date: string
  is_published: boolean
  is_major: boolean
  author?: string
  tags?: string
  created_at: string
  updated_at: string
}

export function VersionLayout({
  version,
  children,
}: {
  version: WebsiteVersion
  children: React.ReactNode
}) {
  let router = useRouter()
  let { previousPathname } = useContext(AppContext)
  const [contentString, setContentString] = React.useState('')

  // 提取内容用于目录生成
  React.useEffect(() => {
    // 尝试多种方式提取内容
    let extractedContent = ''

    if (React.isValidElement(children) && children.props?.content) {
      extractedContent = children.props.content
    } else if (version.content) {
      extractedContent = version.content
    }

    setContentString(extractedContent)
  }, [children, version])

  const getTags = (tagsString?: string) => {
    if (!tagsString) return []
    return tagsString.split(',').map(tag => tag.trim()).filter(Boolean)
  }

  // 使用与博客页一致的标签样式
  const getTagStyle = (color?: string | null) => tagStyleGenerator.getTagStyle(color, 'minimal')
  const getTagClasses = (color?: string | null) => tagStyleGenerator.getTagClasses(color, 'minimal')

  // 增强的标签悬停效果
  const getEnhancedTagStyle = (color?: string | null) => {
    const baseStyle = getTagStyle(color)
    return {
      ...baseStyle,
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      transform: 'perspective(100px) translateZ(0)',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1)',
    }
  }

  const tags = getTags(version.tags)

  return (
    <PageTransition>
      <div className="relative min-h-screen">
        {/* 全宽布局容器 */}
        <FullWidthContainer
          variant="constrained"
          maxWidth="5xl"
          padding="md"
          className="mt-16 lg:mt-32"
        >
          <div className="xl:relative">

            {/* 主内容区域 - 渐进式全宽布局 */}
            <ContentContainer
              contentType="article"
              responsive="progressive"
              spacing="none"
              className="min-h-0"
            >
              {previousPathname && (
                <ContentTransition delay={0.1}>
                  <div className="mb-8">
                    <button
                      type="button"
                      onClick={() => router.back()}
                      aria-label="Go back to version history"
                      className="group flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-white shadow-md shadow-zinc-800/5 ring-1 ring-zinc-900/5 transition hover:shadow-lg hover:scale-105 dark:border dark:border-zinc-700/50 dark:bg-zinc-800 dark:ring-0 dark:ring-white/10 dark:hover:border-zinc-700 dark:hover:ring-white/20"
                    >
                      <ArrowLeftIcon className="h-4 w-4 sm:h-5 sm:w-5 stroke-zinc-500 transition group-hover:stroke-zinc-700 dark:stroke-zinc-500 dark:group-hover:stroke-zinc-400" />
                    </button>
                  </div>
                </ContentTransition>
              )}

              <article>
                {/* 增强的版本头部 - 3D卡片效果 */}
                <ContentTransition delay={0.2}>
                  <SectionContainer
                    section="header"
                    layout="contained"
                    spacing="xl"
                    background="subtle"
                    className="mb-16 rounded-3xl border border-border/50 backdrop-blur-sm shadow-2xl hover:shadow-3xl hover:shadow-primary/10 transition-all duration-700 group/header overflow-hidden"
                    style={{
                      transform: 'perspective(1000px) translateZ(0)',
                      transformStyle: 'preserve-3d',
                      boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)'
                    }}
                  >
                {/* 3D背景效果 */}
                <div className="absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/5 to-primary/0 opacity-0 group-hover/header:opacity-100 transition-all duration-500" />
                <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary/5 opacity-0 group-hover/header:opacity-100 transition-all duration-700" />

                {/* 边缘高光 */}
                <div className="absolute inset-0 rounded-3xl border border-white/20 opacity-0 group-hover/header:opacity-100 transition-opacity duration-300" />

                {/* 顶部装饰光带 */}
                <div className="absolute top-0 left-1/4 right-1/4 h-0.5 bg-gradient-to-r from-transparent via-primary/60 to-transparent opacity-60" />

                {/* 角落装饰 */}
                <div className="absolute top-4 left-4 w-8 h-8 border-l-2 border-t-2 border-primary/30 rounded-tl-lg opacity-0 group-hover/header:opacity-100 transition-opacity duration-500" />
                <div className="absolute top-4 right-4 w-8 h-8 border-r-2 border-t-2 border-primary/30 rounded-tr-lg opacity-0 group-hover/header:opacity-100 transition-opacity duration-500" />

                <div className="relative z-10 space-y-6">
                  {/* 状态标签区域 - 增强视觉效果 */}
                  <div className="flex flex-wrap gap-3">
                    <span className={`relative inline-flex items-center px-4 py-2 text-xs font-bold rounded-full hover:scale-105 hover:shadow-lg transition-all duration-300 cursor-default overflow-hidden ${
                      version.is_major
                        ? 'bg-gradient-to-r from-red-500/15 to-orange-500/15 border-2 border-red-500/30 text-red-700 dark:text-red-300 hover:shadow-red-500/25'
                        : 'bg-gradient-to-r from-blue-500/15 to-cyan-500/15 border-2 border-blue-500/30 text-blue-700 dark:text-blue-300 hover:shadow-blue-500/25'
                    }`}>
                      {/* 背景光效 */}
                      <div className={`absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-300 ${
                        version.is_major
                          ? 'bg-gradient-to-r from-red-500/20 to-orange-500/20'
                          : 'bg-gradient-to-r from-blue-500/20 to-cyan-500/20'
                      }`} />
                      <span className="relative z-10 tracking-wide">{version.version}</span>
                    </span>
                    {version.is_major && (
                      <span className="relative inline-flex items-center gap-1.5 px-4 py-2 text-xs font-bold bg-gradient-to-r from-orange-500/15 to-red-500/15 border-2 border-orange-500/30 rounded-full text-orange-700 dark:text-orange-300 hover:scale-105 hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-300 cursor-default overflow-hidden">
                        {/* 背景光效 */}
                        <div className="absolute inset-0 bg-gradient-to-r from-orange-500/20 to-red-500/20 opacity-0 hover:opacity-100 transition-opacity duration-300" />
                        <Star className="w-3 h-3 fill-current animate-pulse-soft relative z-10" />
                        <span className="relative z-10 tracking-wide">MAJOR RELEASE</span>
                      </span>
                    )}
                  </div>

                  {/* 增强的标题 - 使用更具区分度的字体 */}
                  <h1
                    className="text-4xl sm:text-5xl lg:text-6xl font-black tracking-tight text-foreground leading-tight break-words group-hover/header:text-primary transition-all duration-300 drop-shadow-sm group-hover/header:drop-shadow-md"
                    style={{
                      transform: 'translateZ(20px)',
                      transformStyle: 'preserve-3d',
                      fontFamily: 'ui-serif, Georgia, Cambria, "Times New Roman", Times, serif'
                    }}
                  >
                    {version.title}
                  </h1>

                  {/* 元信息区域 - 增强视觉效果 */}
                  <div className="flex flex-wrap items-center gap-8 text-sm text-muted-foreground">
                    {/* 发布日期 */}
                    <div className="flex items-center gap-3 group/meta px-4 py-2 rounded-xl bg-gradient-to-r from-slate-50/50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50 border border-border/30 hover:border-primary/30 transition-all duration-300">
                      <div className="p-1.5 rounded-lg bg-primary/10 text-primary group-hover/meta:bg-primary/20 group-hover/meta:scale-110 transition-all duration-300">
                        <Calendar className="w-4 h-4" />
                      </div>
                      <time dateTime={version.release_date} className="group-hover/meta:text-primary transition-colors duration-300 font-medium">
                        {formatDate(version.release_date)}
                      </time>
                    </div>

                    {/* 作者信息 */}
                    {version.author && (
                      <div className="flex items-center gap-3 group/meta px-4 py-2 rounded-xl bg-gradient-to-r from-slate-50/50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50 border border-border/30 hover:border-primary/30 transition-all duration-300">
                        <div className="p-1.5 rounded-lg bg-blue-500/10 text-blue-500 group-hover/meta:bg-blue-500/20 group-hover/meta:scale-110 transition-all duration-300">
                          <User className="w-4 h-4" />
                        </div>
                        <span className="group-hover/meta:text-primary transition-colors duration-300 font-medium">Released by {version.author}</span>
                      </div>
                    )}

                    {/* 浏览量统计 */}
                    <div className="flex items-center gap-3 group/meta px-4 py-2 rounded-xl bg-gradient-to-r from-slate-50/50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50 border border-border/30 hover:border-primary/30 transition-all duration-300">
                      <div className="p-1.5 rounded-lg bg-green-500/10 text-green-500 group-hover/meta:bg-green-500/20 group-hover/meta:scale-110 transition-all duration-300">
                        <Eye className="w-4 h-4" />
                      </div>
                      <PageViewCount
                        path={`/version-history/${version.id}`}
                        showLabel={false}
                        className="group-hover/meta:text-primary transition-colors duration-300 font-medium"
                      />
                    </div>

                    {/* 版本类型 */}
                    <div className="flex items-center gap-3 group/meta px-4 py-2 rounded-xl bg-gradient-to-r from-slate-50/50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50 border border-border/30 hover:border-primary/30 transition-all duration-300">
                      <div className="p-1.5 rounded-lg bg-purple-500/10 text-purple-500 group-hover/meta:bg-purple-500/20 group-hover/meta:scale-110 transition-all duration-300">
                        <BookOpen className="w-4 h-4" />
                      </div>
                      <span className="group-hover/meta:text-primary transition-colors duration-300 font-medium">Version Update</span>
                    </div>

                    {/* Waline评论统计 */}
                    <CommentStats path={`/versions/${version.id}`} showIcons={true} />
                  </div>

                  {/* 版本摘要装饰 */}
                  <div className="space-y-4">
                    {/* 描述装饰线 */}
                    <div className="flex items-center gap-4 mb-8">
                      <div className="h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent flex-1" />
                      <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-full shadow-sm">
                        <BookOpen className="w-4 h-4 text-primary/70 group-hover/header:text-primary group-hover/header:scale-110 transition-all duration-300" />
                        <span className="text-sm font-semibold text-primary/80 group-hover/header:text-primary transition-colors duration-300 tracking-wide">
                          ABSTRACT
                        </span>
                      </div>
                      <div className="h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent flex-1" />
                    </div>

                    {/* 版本摘要内容 */}
                    <div className="relative p-8 sm:p-10 rounded-3xl bg-gradient-to-br from-slate-50/80 via-slate-50/40 to-transparent dark:from-slate-800/40 dark:via-slate-800/20 dark:to-transparent border-2 border-primary/10 backdrop-blur-md group-hover/header:border-primary/25 transition-all duration-500 overflow-hidden shadow-lg shadow-primary/5">
                      {/* 描述背景光效 */}
                      <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-primary/5 via-primary/2 to-transparent opacity-0 group-hover/header:opacity-100 transition-opacity duration-500" />

                      {/* 装饰性光点 */}
                      <div className="absolute top-6 right-6 w-3 h-3 bg-primary/20 rounded-full blur-sm animate-pulse-soft" />
                      <div className="absolute bottom-6 left-6 w-2 h-2 bg-secondary/30 rounded-full blur-sm animate-pulse-soft" style={{ animationDelay: '1s' }} />

                      {/* 摘要文字 - 使用更优雅的字体 */}
                      <div className="relative z-10">
                        <p
                          className="text-lg sm:text-xl leading-relaxed text-muted-foreground group-hover/header:text-foreground transition-colors duration-300 font-medium"
                          style={{
                            fontFamily: 'ui-serif, Georgia, Cambria, "Times New Roman", Times, serif',
                            fontStyle: 'italic'
                          }}
                        >
                          This version introduces new features, improvements, and bug fixes to enhance the overall user experience and system performance.
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* 增强的标签区域 */}
                  {tags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {tags.map((tag, index) => (
                        <span
                          key={index}
                          className={cn(
                            getTagClasses('#10B981'), // 使用主题绿色
                            "hover:scale-110 hover:-translate-y-1 hover:shadow-lg hover:shadow-primary/20 active:scale-95 group/tag relative overflow-hidden"
                          )}
                          style={getEnhancedTagStyle('#10B981')}
                        >
                          {/* 标签背景光晕 */}
                          <div
                            className="absolute inset-0 opacity-0 group-hover/tag:opacity-20 transition-opacity duration-300 blur-sm"
                            style={{ backgroundColor: '#10B981' }}
                          />

                          {/* 标签闪光效果 */}
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent translate-x-[-100%] group-hover/tag:translate-x-[100%] transition-transform duration-500" />

                          {/* 标签内容 */}
                          <span className="relative z-10 group-hover/tag:text-shadow-sm transition-all duration-300">
                            {tag}
                          </span>

                          {/* 点击涟漪效果 */}
                          <div className="absolute inset-0 opacity-0 group-active/tag:opacity-30 bg-white rounded-md scale-0 group-active/tag:scale-100 transition-all duration-200" />
                        </span>
                      ))}
                    </div>
                  )}
                  </div>
                  </SectionContainer>
                </ContentTransition>

                {/* 版本内容 */}
                <ContentTransition delay={0.3}>
                  <SectionContainer
                    section="content"
                    layout="contained"
                    spacing="lg"
                    data-mdx-content
                  >
                    <Prose className="mt-8">
                      {children}
                    </Prose>
                  </SectionContainer>
                </ContentTransition>
              </article>
            </ContentContainer>

            {/* 目录导航 - 优化的响应式定位 */}
            <div className="hidden 2xl:block fixed right-4 top-1/2 transform -translate-y-1/2 w-80 z-40 pointer-events-auto">
              <ResponsiveTableOfContents content={contentString} />
            </div>
            {/* 中等大屏幕的目录导航 */}
            <div className="hidden xl:block 2xl:hidden fixed right-2 top-1/2 transform -translate-y-1/2 w-72 z-40 pointer-events-auto">
              <ResponsiveTableOfContents content={contentString} />
            </div>

            {/* 移动端和中等屏幕目录导航 */}
            <div className="xl:hidden">
              <ResponsiveTableOfContents content={contentString} />
            </div>
          </div>
        </FullWidthContainer>
      </div>
    </PageTransition>
  )
}

function ArrowLeftIcon(props: React.ComponentPropsWithoutRef<'svg'>) {
  return (
    <svg viewBox="0 0 16 16" fill="none" aria-hidden="true" {...props}>
      <path
        d="m9.25 10.75-3.5-3.5 3.5-3.5"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
