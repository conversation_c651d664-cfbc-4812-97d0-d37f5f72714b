import { forwardRef } from 'react'
import clsx from 'clsx'

interface FullWidthContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * 容器变体
   * - full: 完全全宽，无任何限制
   * - constrained: 有最大宽度限制的全宽
   * - fluid: 流体布局，根据内容自适应
   */
  variant?: 'full' | 'constrained' | 'fluid'
  
  /**
   * 最大宽度设置（仅在constrained模式下生效）
   */
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | 'none'
  
  /**
   * 内边距设置
   */
  padding?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  
  /**
   * 是否居中对齐
   */
  center?: boolean
  
  /**
   * 背景样式
   */
  background?: 'none' | 'subtle' | 'card' | 'gradient'
  
  /**
   * 是否启用过渡动画
   */
  animated?: boolean
}

/**
 * 全宽容器组件 - 现代化布局系统的核心
 * 支持多种布局模式，适配从手机到8K显示器的所有设备
 */
export const FullWidthContainer = forwardRef<HTMLDivElement, FullWidthContainerProps>(
  ({ 
    variant = 'constrained',
    maxWidth = '5xl',
    padding = 'md',
    center = true,
    background = 'none',
    animated = true,
    className,
    children,
    ...props 
  }, ref) => {
    
    // 最大宽度映射
    const maxWidthClasses = {
      sm: 'max-w-2xl',
      md: 'max-w-4xl', 
      lg: 'max-w-6xl',
      xl: 'max-w-7xl',
      '2xl': 'max-w-screen-2xl',
      '3xl': 'max-w-screen-3xl',
      '4xl': 'max-w-[2560px]',
      '5xl': 'max-w-[3840px]',
      none: 'max-w-none'
    }

    // 内边距映射 - 响应式设计
    const paddingClasses = {
      none: '',
      xs: 'px-2 sm:px-4',
      sm: 'px-4 sm:px-6',
      md: 'px-4 sm:px-6 lg:px-8',
      lg: 'px-6 sm:px-8 lg:px-12',
      xl: 'px-8 sm:px-12 lg:px-16',
      '2xl': 'px-8 sm:px-16 lg:px-24 xl:px-32'
    }

    // 背景样式映射
    const backgroundClasses = {
      none: '',
      subtle: 'bg-background/50 backdrop-blur-sm',
      card: 'bg-card border border-border/50 shadow-sm',
      gradient: 'bg-gradient-to-br from-background via-background to-muted/20'
    }

    // 变体样式映射
    const variantClasses = {
      full: 'w-full',
      constrained: clsx('w-full', maxWidthClasses[maxWidth]),
      fluid: 'w-full min-w-0'
    }

    return (
      <div
        ref={ref}
        className={clsx(
          // 基础样式
          'relative',
          
          // 变体样式
          variantClasses[variant],
          
          // 居中对齐
          center && variant !== 'full' && 'mx-auto',
          
          // 内边距
          paddingClasses[padding],
          
          // 背景样式
          backgroundClasses[background],
          
          // 过渡动画
          animated && 'transition-all duration-300 ease-in-out',
          
          // 自定义类名
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)

FullWidthContainer.displayName = 'FullWidthContainer'
