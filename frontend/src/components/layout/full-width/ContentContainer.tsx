import { forwardRef } from 'react'
import clsx from 'clsx'

interface ContentContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  /**
   * 内容类型 - 影响最佳阅读宽度
   * - article: 文章内容，优化阅读体验
   * - wide: 宽内容，如图片、表格等
   * - full: 全宽内容
   */
  contentType?: 'article' | 'wide' | 'full'
  
  /**
   * 响应式行为
   * - adaptive: 根据屏幕尺寸自适应
   * - fixed: 固定宽度
   * - progressive: 渐进式扩展
   */
  responsive?: 'adaptive' | 'fixed' | 'progressive'
  
  /**
   * 垂直间距
   */
  spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  
  /**
   * 是否启用阅读优化
   */
  readingOptimized?: boolean
}

/**
 * 内容容器组件 - 专为详情页内容设计
 * 提供最佳的阅读体验和视觉层次
 */
export const ContentContainer = forwardRef<HTMLDivElement, ContentContainerProps>(
  ({ 
    contentType = 'article',
    responsive = 'progressive',
    spacing = 'md',
    readingOptimized = true,
    className,
    children,
    ...props 
  }, ref) => {
    
    // 内容类型样式映射
    const contentTypeClasses = {
      // 文章内容 - 优化阅读体验，65-75字符每行
      article: clsx(
        responsive === 'progressive' && [
          // 渐进式扩展：从传统宽度逐步扩展到更宽
          'max-w-2xl',           // 小屏：传统阅读宽度
          'sm:max-w-3xl',        // 中屏：适度扩展
          'md:max-w-4xl',        // 平板：进一步扩展
          'lg:max-w-5xl',        // 桌面：宽阅读体验
          'xl:max-w-6xl',        // 大屏：充分利用空间
          '2xl:max-w-7xl',       // 超大屏：最大化利用
          '3xl:max-w-[1600px]',  // 超宽屏：限制过宽
          '4xl:max-w-[1800px]',  // 4K：保持可读性
        ],
        responsive === 'adaptive' && [
          // 自适应：根据屏幕比例调整
          'max-w-[85vw]',        // 基于视口宽度
          'sm:max-w-[80vw]',
          'lg:max-w-[75vw]',
          'xl:max-w-[70vw]',
          '2xl:max-w-[65vw]',
          '3xl:max-w-[60vw]',
        ],
        responsive === 'fixed' && 'max-w-4xl'
      ),
      
      // 宽内容 - 适合图片、表格、代码等
      wide: clsx(
        responsive === 'progressive' && [
          'max-w-4xl',
          'sm:max-w-5xl',
          'md:max-w-6xl',
          'lg:max-w-7xl',
          'xl:max-w-screen-xl',
          '2xl:max-w-screen-2xl',
          '3xl:max-w-screen-3xl',
        ],
        responsive === 'adaptive' && [
          'max-w-[95vw]',
          'sm:max-w-[90vw]',
          'lg:max-w-[85vw]',
        ],
        responsive === 'fixed' && 'max-w-6xl'
      ),
      
      // 全宽内容
      full: 'max-w-none w-full'
    }

    // 垂直间距映射
    const spacingClasses = {
      none: '',
      sm: 'py-4',
      md: 'py-6 lg:py-8',
      lg: 'py-8 lg:py-12',
      xl: 'py-12 lg:py-16'
    }

    // 阅读优化样式
    const readingOptimizedClasses = readingOptimized ? [
      // 行高优化
      'leading-relaxed',
      // 字间距优化
      'tracking-wide',
      // 段落间距
      '[&>*+*]:mt-6',
      // 标题间距
      '[&>h1]:mt-12 [&>h1]:mb-6',
      '[&>h2]:mt-10 [&>h2]:mb-5',
      '[&>h3]:mt-8 [&>h3]:mb-4',
      '[&>h4]:mt-6 [&>h4]:mb-3',
    ] : []

    return (
      <div
        ref={ref}
        className={clsx(
          // 基础样式
          'relative mx-auto',
          
          // 内容类型样式
          contentTypeClasses[contentType],
          
          // 垂直间距
          spacingClasses[spacing],
          
          // 阅读优化
          readingOptimizedClasses,
          
          // 过渡动画
          'transition-all duration-300 ease-in-out',
          
          // 自定义类名
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)

ContentContainer.displayName = 'ContentContainer'
