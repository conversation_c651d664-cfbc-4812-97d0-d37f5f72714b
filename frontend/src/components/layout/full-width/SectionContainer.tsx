import { forwardRef } from 'react'
import clsx from 'clsx'

interface SectionContainerProps extends React.HTMLAttributes<HTMLElement> {
  /**
   * 区段类型
   * - header: 页面头部区域
   * - content: 主要内容区域
   * - sidebar: 侧边栏区域
   * - footer: 页面底部区域
   */
  section?: 'header' | 'content' | 'sidebar' | 'footer'
  
  /**
   * 布局模式
   * - contained: 有容器限制
   * - full: 全宽布局
   * - bleed: 出血布局（超出容器边界）
   */
  layout?: 'contained' | 'full' | 'bleed'
  
  /**
   * 垂直间距
   */
  spacing?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  
  /**
   * 背景处理
   */
  background?: 'none' | 'subtle' | 'accent' | 'contrast'
  
  /**
   * 是否启用视觉分隔
   */
  divider?: boolean
  
  /**
   * HTML标签类型
   */
  as?: 'div' | 'section' | 'header' | 'main' | 'aside' | 'footer'
}

/**
 * 区段容器组件 - 用于页面区域划分
 * 提供灵活的布局选项和视觉层次
 */
export const SectionContainer = forwardRef<HTMLElement, SectionContainerProps>(
  ({ 
    section = 'content',
    layout = 'contained',
    spacing = 'md',
    background = 'none',
    divider = false,
    as = 'div',
    className,
    children,
    ...props 
  }, ref) => {
    
    const Component = as
    
    // 区段特定样式
    const sectionClasses = {
      header: [
        'relative z-10',
        layout === 'full' && 'w-full',
        layout === 'contained' && 'max-w-7xl mx-auto',
        layout === 'bleed' && 'w-screen relative left-1/2 right-1/2 -mx-[50vw]'
      ],
      content: [
        'relative',
        layout === 'full' && 'w-full',
        layout === 'contained' && 'max-w-7xl mx-auto',
        layout === 'bleed' && 'w-screen relative left-1/2 right-1/2 -mx-[50vw]'
      ],
      sidebar: [
        'relative',
        'w-full lg:w-auto'
      ],
      footer: [
        'relative',
        layout === 'full' && 'w-full',
        layout === 'contained' && 'max-w-7xl mx-auto',
        layout === 'bleed' && 'w-screen relative left-1/2 right-1/2 -mx-[50vw]'
      ]
    }

    // 垂直间距映射
    const spacingClasses = {
      none: '',
      xs: 'py-2',
      sm: 'py-4',
      md: 'py-6 lg:py-8',
      lg: 'py-8 lg:py-12',
      xl: 'py-12 lg:py-16',
      '2xl': 'py-16 lg:py-24'
    }

    // 背景样式映射
    const backgroundClasses = {
      none: '',
      subtle: 'bg-muted/30',
      accent: 'bg-accent/10',
      contrast: 'bg-card border-y border-border/50'
    }

    // 分隔线样式
    const dividerClasses = divider ? [
      'border-b border-border/20',
      'after:absolute after:bottom-0 after:left-0 after:right-0',
      'after:h-px after:bg-gradient-to-r after:from-transparent after:via-border/50 after:to-transparent'
    ] : []

    return (
      <Component
        ref={ref as any}
        className={clsx(
          // 基础样式
          'relative',
          
          // 区段特定样式
          sectionClasses[section],
          
          // 垂直间距
          spacingClasses[spacing],
          
          // 背景样式
          backgroundClasses[background],
          
          // 分隔线
          dividerClasses,
          
          // 过渡动画
          'transition-all duration-300 ease-in-out',
          
          // 自定义类名
          className
        )}
        {...props}
      >
        {children}
      </Component>
    )
  }
)

SectionContainer.displayName = 'SectionContainer'
