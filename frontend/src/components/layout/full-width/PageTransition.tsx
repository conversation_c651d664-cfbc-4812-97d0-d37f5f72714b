'use client'

import { ReactNode } from 'react'

interface PageTransitionProps {
  children: ReactNode
  className?: string
}

/**
 * 页面过渡动画组件
 * 专为全宽布局优化，确保从列表页到详情页的平滑过渡
 * 临时简化版本，使用CSS动画替代framer-motion
 */
export function PageTransition({ children, className }: PageTransitionProps) {
  return (
    <div
      className={`animate-fade-in-up ${className || ''}`}
      style={{
        animationDuration: '0.6s',
        animationTimingFunction: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
      }}
    >
      {children}
    </div>
  )
}

/**
 * 内容区域过渡动画
 * 用于详情页内容的渐进式显示
 * 临时简化版本，使用CSS动画替代framer-motion
 */
export function ContentTransition({
  children,
  delay = 0,
  className
}: {
  children: ReactNode
  delay?: number
  className?: string
}) {
  return (
    <div
      className={`animate-fade-in-up ${className || ''}`}
      style={{
        animationDelay: `${delay * 100}ms`,
        animationDuration: '0.5s',
        animationTimingFunction: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        animationFillMode: 'both'
      }}
    >
      {children}
    </div>
  )
}

/**
 * 宽度过渡动画
 * 专门用于容器宽度的平滑变化
 * 临时简化版本，使用CSS动画替代framer-motion
 */
export function WidthTransition({
  children,
  fromWidth = 'max-w-5xl',
  toWidth = 'max-w-7xl',
  className
}: {
  children: ReactNode
  fromWidth?: string
  toWidth?: string
  className?: string
}) {
  return (
    <div
      className={`${toWidth} mx-auto transition-all duration-700 ease-out animate-fade-in ${className || ''}`}
    >
      {children}
    </div>
  )
}
