'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { usePathname } from 'next/navigation'
import { ReactNode } from 'react'

interface PageTransitionProps {
  children: ReactNode
  className?: string
}

/**
 * 页面过渡动画组件
 * 专为全宽布局优化，确保从列表页到详情页的平滑过渡
 */
export function PageTransition({ children, className }: PageTransitionProps) {
  const pathname = usePathname()
  
  // 检测页面类型
  const isDetailPage = pathname.includes('/blogs/') || 
                      pathname.includes('/projects/') || 
                      pathname.includes('/version-history/')
  
  const isListPage = pathname === '/blogs' || 
                    pathname === '/projects' || 
                    pathname === '/version-history'

  // 详情页进入动画 - 从窄到宽的扩展效果
  const detailPageVariants = {
    initial: {
      opacity: 0,
      scale: 0.95,
      // 模拟从列表页容器宽度开始
      maxWidth: '1280px',
      margin: '0 auto'
    },
    animate: {
      opacity: 1,
      scale: 1,
      // 扩展到全宽
      maxWidth: '100%',
      margin: '0',
      transition: {
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94],
        maxWidth: {
          duration: 0.8,
          ease: [0.25, 0.46, 0.45, 0.94]
        }
      }
    },
    exit: {
      opacity: 0,
      scale: 0.98,
      transition: {
        duration: 0.3,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  }

  // 列表页动画 - 标准淡入淡出
  const listPageVariants = {
    initial: {
      opacity: 0,
      y: 20
    },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    },
    exit: {
      opacity: 0,
      y: -10,
      transition: {
        duration: 0.3,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  }

  // 默认动画
  const defaultVariants = {
    initial: {
      opacity: 0,
      y: 10
    },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    },
    exit: {
      opacity: 0,
      transition: {
        duration: 0.2,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  }

  // 选择合适的动画变体
  const variants = isDetailPage ? detailPageVariants : 
                  isListPage ? listPageVariants : 
                  defaultVariants

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={pathname}
        initial="initial"
        animate="animate"
        exit="exit"
        variants={variants}
        className={className}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  )
}

/**
 * 内容区域过渡动画
 * 用于详情页内容的渐进式显示
 */
export function ContentTransition({ 
  children, 
  delay = 0,
  className 
}: { 
  children: ReactNode
  delay?: number
  className?: string 
}) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ 
        opacity: 1, 
        y: 0,
        transition: {
          duration: 0.5,
          delay,
          ease: [0.25, 0.46, 0.45, 0.94]
        }
      }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

/**
 * 宽度过渡动画
 * 专门用于容器宽度的平滑变化
 */
export function WidthTransition({ 
  children, 
  fromWidth = 'max-w-5xl',
  toWidth = 'max-w-7xl',
  className 
}: { 
  children: ReactNode
  fromWidth?: string
  toWidth?: string
  className?: string 
}) {
  return (
    <motion.div
      initial={{ 
        opacity: 0,
      }}
      animate={{ 
        opacity: 1,
        transition: {
          duration: 0.6,
          ease: [0.25, 0.46, 0.45, 0.94]
        }
      }}
      className={`${toWidth} mx-auto transition-all duration-700 ease-out ${className}`}
    >
      {children}
    </motion.div>
  )
}
